// @ts-check

/**
 * Subtitle Translation Application and Formatting
 *
 * This script performs the following tasks:
 *
 * 1. Processes English .ass subtitle files and applies translations from corresponding .txt files.
 * 2. Implements robust error handling and logging mechanisms:
 *    - Logs information, errors, uncaught exceptions, and unhandled rejections to separate files.
 *    - Uses color-coded console output for better readability and error distinction.
 * 3. Manages file operations for reading input files and writing processed outputs.
 * 4. Implements a graceful shutdown process to ensure proper resource cleanup.
 * 5. Utilizes environment variables for configuration (loaded via dotenv).
 * 6. Provides utility functions for directory creation, timestamp generation, and error message cleaning.
 * 7. Overrides console.info and console.error methods to enhance logging capabilities.
 * 8. Implements handlers for uncaught exceptions and unhandled rejections.
 * 9. Sets up a SIGINT handler for graceful shutdown on process interruption.
 * 10. Applies extensive style formatting to the .ass files, including font size, outline, and margin adjustments.
 * 11. Manages the execution state of the script using an IS_RUNNING_FILE.
 * 12. Extracts anime title and episode number from filenames.
 * 13. Converts style names in dialogue lines and style tags to lowercase for consistency.
 * 14. Implements functions to trim trailing newlines from files.
 * 15. Provides functionality to delete all files in specified directories as part of cleanup.
 * 16. Includes a function to close file handles, particularly useful for files in the downloads directory.
 * 17. Posts translated subtitles to a Discord webhook for distribution or notification.
 * 18. Utilizes child processes to execute system commands for file handle management.
 * 19. Implements custom solutions for specific anime titles or formatting requirements.
 *
 * The script is designed as a comprehensive solution for applying translations to anime subtitles,
 * with a focus on maintaining consistent formatting, error handling, and efficient file management.
 * It serves as the final step in a subtitle translation pipeline, preparing the subtitles for
 * distribution and ensuring they meet specific quality and formatting standards.
 */

import { promisify } from "util";
const execAsync = promisify(cp.exec);
import { config } from "dotenv";
config();
import process from "process";
import fs from "fs";
import path from "path";
import * as cp from "child_process";
import axios from "axios";
import FormData from "form-data";
const subWebhookUrl = process.env.DISCORD_SUB_INFO;

const IS_RUNNING_FILE = "app/isRunning.txt";

/**
 * @constant {Object} COLORS - ANSI escape codes for text formatting.
 */
const COLORS = {
  RESET: "\x1b[0m",
  BLACK: "\x1b[30m",
  RED: "\x1b[31m",
  GREEN: "\x1b[32m",
  YELLOW: "\x1b[33m",
  BLUE: "\x1b[34m",
  MAGENTA: "\x1b[35m",
  CYAN: "\x1b[36m",
  WHITE: "\x1b[37m",
  GRAY: "\x1b[90m",
  DIM: "\x1b[2m",
  BG_BLACK: "\x1b[40m",
  BG_RED: "\x1b[41m",
  BG_GREEN: "\x1b[42m",
  BG_YELLOW: "\x1b[43m",
  BG_BLUE: "\x1b[44m",
  BG_MAGENTA: "\x1b[45m",
  BG_CYAN: "\x1b[46m",
  BG_WHITE: "\x1b[47m",
};

const logsDir = "app/logs";
createDirectoryIfNotExists(logsDir);
const infoLogStream = createWriteStream(path.join(logsDir, "info.log"));
const errorLogStream = createWriteStream(path.join(logsDir, "error.log"));
const exceptionLogStream = createWriteStream(path.join(logsDir, "exception.log"));
const rejectionLogStream = createWriteStream(path.join(logsDir, "rejection.log"));

setupLogging();
setupUncaughtErrorHandling();

main();

/**
 * Main function to process files and start applying translation from .txt files to English .ass files.
 * @returns {Promise<void>}
 * @throws {Error} If an error occurs during script execution.
 */
async function main() {
  try {
    await processDirectories("app/1clear/extracted", "app/3replace/clean");
    await performDirectoryCleanup(["app/1clear/extracted", "app/3replace/clean", "app/0rss/downloads", "app/audio_processing"]);
    stopRunning();
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] An error occurred during script execution: ${COLORS.RESET}`, error);
    throw error;
  }
}

const SERIES_NOTIFICATIONS = {
  "Summer Pockets": ["436629443826548736"],
  "Isekai Mokushiroku Mynoghra ~Hametsu no Bunmei de Hajimeru Sekai Seifuku~": ["289843490727133185"],
  "Sono Bisque Doll wa Koi wo Suru Season 2": ["289843490727133185"],
  "To Be Hero X": ["302781404473982977"],
  "Anne Shirley": ["436629443826548736"],
  "Kijin Gentoushou": ["185126303677153281"],
  "Shin Samurai-den Yaiba": ["185126303677153281"],
  "Food Court de, Mata Ashita.": ["185126303677153281"],
  "Grand Blue Season 2": ["436629443826548736"],
  "Watanare": ["436629443826548736"],
  "Kanojo, Okarishimasu 4th Season": ["185126303677153281"],
  "Kakkou no Iinazuke Season 2": ["185126303677153281"],
  "Mattaku Saikin no Tantei to Kitara": ["185126303677153281"],
  "Jidou Hanbaiki ni Umarekawatta Ore wa Meikyuu wo Samayou 2nd Season": ["185126303677153281"],
  "Dainanaoji S2": ["185126303677153281"],
  "Mikadono Sanshimai wa Angai, Choroi.": ["185126303677153281"],
  "Futari Solo Camp": ["185126303677153281"],
  "Mizu Zokusei no Mahoutsukai Daiichibu": ["185126303677153281"],
  "Yofukashi no Uta S2": ["185126303677153281"],
  "Silent Witch - Chinmoku no Majo no Kakushigoto": ["185126303677153281"],
  "Takopii no Genzai": ["185126303677153281"],
  "Watari-kun no xx ga Houkai Sunzen": ["1110288581915836417"],
  "NUKITASHI THE ANIMATION": ["436629443826548736"],
  "9-nine- Ruler's Crown": ["436629443826548736"],
  "Kaijuu 8-gou 2nd Season": ["185126303677153281"],
  "Kizetsu Yuusha to Ansatsu Hime": ["185126303677153281"],
  "Seishun Buta Yarou wa Santa Claus no Yume wo Minai": ["436629443826548736"],
  "Fermat no Ryouri": ["185126303677153281"],
  "Bad Girl": ["185126303677153281"],
  "Koujo Denka no Kateikyoushi": ["185126303677153281"],
  "Jibaku Shounen Hanako-kun 2 Part 2": ["185126303677153281"],
  "GACHIAKUTA": ["436629443826548736"],
  "Uglymug Epicfighter": ["436629443826548736"],
  "Game Center Shoujo to Ibunka Kouryuu": ["185126303677153281"],
  "Hotel Inhumans": ["185126303677153281"],
};

// Default users to notify if series isn't in mapping
const DEFAULT_NOTIFY = ["185126303677153281"];

async function postTranslatedSub(PLSubPath, title, episodeNumber) {
  const fileContent = fs.readFileSync(PLSubPath, "utf8");
  const buffer = Buffer.from(fileContent, "utf8");

  // Get users to notify based on series title
  const usersToNotify = SERIES_NOTIFICATIONS[title] || DEFAULT_NOTIFY;
  const userMentions = usersToNotify.map((id) => `<@${id}>`).join("");

  const formData = new FormData();
  formData.append("content", `**${title} - ${episodeNumber}**\n${userMentions}`);
  formData.append("file", buffer, {
    filename: `[lycoris.cafe] ${title} - ${episodeNumber}.ass`,
    contentType: "application/octet-stream",
  });

  try {
    // @ts-ignore
    const response = await axios.post(subWebhookUrl, formData, {
      headers: {
        ...formData.getHeaders(),
      },
    });

    if (response.status === 200) {
      console.log("Subtitles sent successfully");
    } else {
      console.error("Failed to send subtitles:", response.status, response.statusText);
    }
  } catch (error) {
    console.error("Error sending subtitles:", error.message);
  }
}

/**
 * @description Processes directories containing English .ass and .txt files.
 * @param {string} assDirPath - Path to the directory containing .ass files.
 * @param {string} translationDirPath - Path to the directory containing .txt translation files.
 * @returns {Promise<void>}
 * @throws {Error} If the number of English .ass files and .txt translation files is not the same.
 */
async function processDirectories(assDirPath, translationDirPath) {
  const assFiles = fs
    .readdirSync(assDirPath)
    .filter((file) => path.extname(file).toLowerCase() === ".ass" && file.toLowerCase().includes("_eng"))
    .sort()
    .map((file) => path.join(assDirPath, file));

  const translationFiles = fs
    .readdirSync(translationDirPath)
    .filter((file) => path.extname(file).toLowerCase() === ".txt")
    .sort()
    .map((file) => path.join(translationDirPath, file));

  if (assFiles.length !== translationFiles.length) {
    throw new Error(`${COLORS.RED}[ERROR] The number of English .ass files and .txt translation files is not the same.${COLORS.RESET}`);
  }

  for (let i = 0; i < assFiles.length; i++) {
    await processFile(assFiles[i], translationFiles[i]);
  }
}

/**
 * @description Processes a single English .ass file and its corresponding .txt translation file.
 * @param {string} assFilePath - Path to the English .ass file.
 * @param {string} translationFilePath - Path to the .txt translation file.
 * @returns {Promise<void>}
 * @throws {Error} If an error occurs while processing the file.
 */
async function processFile(assFilePath, translationFilePath) {
  try {
    await trimTrailingNewlines(assFilePath);
    await trimTrailingNewlines(translationFilePath);
    await replaceDialogueLines(assFilePath, translationFilePath);
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Error processing file: ${assFilePath}${COLORS.RESET}`, error);
    throw error;
  }
}

/**
 * @description Replaces dialogue lines in the English .ass file with translations from the .txt file.
 * @param {string} assFilePath - Path to the English .ass file.
 * @param {string} translationFilePath - Path to the .txt translation file.
 * @returns {Promise<void>}
 * @throws {Error} If the number of dialogue lines and translation lines does not match.
 */
async function replaceDialogueLines(assFilePath, translationFilePath) {
  console.info(`${COLORS.GRAY}[INFO] Replacing dialogue lines in file: ${assFilePath}...${COLORS.RESET}`);

  let sourceFileContent = fs.readFileSync(assFilePath, "utf-8");
  // @ts-ignore
  sourceFileContent = stylesInStyleTagToLowerCase(sourceFileContent);
  // @ts-ignore
  sourceFileContent = stylesInDialogueToLowerCase(sourceFileContent);
  fs.writeFileSync(assFilePath, sourceFileContent, "utf-8");

  let dialogueLines = await getDialogueLines(assFilePath);
  let translationLines = await getTranslationLines(translationFilePath);

  if (dialogueLines.length !== translationLines.length) {
    throw new Error(`${COLORS.RED}[ERROR] Mismatched line counts between ${assFilePath} and ${translationFilePath}. .ass file has ${dialogueLines.length} dialogue lines while .txt file has ${translationLines.length} translation lines.${COLORS.RESET}`);
  }

  const customSolutions = ["2.5"];

  let content = fs.readFileSync(assFilePath, "utf8");
  content = applyStyleChanges(content, customSolutions);
  const filename = path.basename(assFilePath);
  content = updateDialogueLines(content, dialogueLines, translationLines);
  const [title, episodeNumber] = extractTitleAndEpisode(filename);

  const PLSubPath = `app/3replace/subPL/[lycoris.cafe] ${title} - ${episodeNumber}.ass`;
  fs.writeFileSync(PLSubPath, content);
  await postTranslatedSub(PLSubPath, title, episodeNumber);
  console.info(`${COLORS.CYAN}[INFO] Dialogue lines replaced for: ${assFilePath}${COLORS.RESET}`);
  console.info(`${COLORS.GRAY}[INFO] Deleted original .txt file: ${translationFilePath}${COLORS.RESET}`);

  fs.unlinkSync(translationFilePath);
  fs.unlinkSync(PLSubPath);
}

/**
 * @description Applies style changes to the .ass file content.
 * @param {string} content - The content of the .ass file.
 * @param {string[]} customSolutions
 * @returns {string} The updated content with style changes applied.
 */
function applyStyleChanges(content, customSolutions) {
  // @ts-ignore
  const PlayResX = content.match(/PlayResX:\s*(.*)/g)[0];
  let fontSize = 28;
  let outlineSize = 1.1;

  if (PlayResX === "PlayResX: 1920") {
    fontSize = 86.4;
    outlineSize = 2.5;
  }

  if (PlayResX === "PlayResX: 1280") {
    fontSize = 54;
    outlineSize = 1.7;
  }

  // Calculate scaled values
  const shadowSize = outlineSize / 4;
  const marginL = Math.round(56 * outlineSize);
  const marginR = Math.round(56 * outlineSize);
  const marginV = Math.round(12 * outlineSize);

  const stylesMarker = "[V4+ Styles]";
  let insertIndex = insertAfterLine(content, stylesMarker);
  let beforeInsert = content.slice(0, insertIndex);
  let afterInsert = content.slice(insertIndex);
  let newLine = `Style: selfplug,Gabarito Medium,${fontSize / 1.904},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize / 1.25},${shadowSize},8,0,0,10,1`;
  content = `${beforeInsert}\n${newLine}\n${afterInsert}`;

  function insertAfterLine(content, marker) {
    const markerIndex = content.indexOf(marker);
    if (markerIndex === -1) return content;
    const endOfMarkerLineIndex = content.indexOf("\n", markerIndex) + 1;
    const insertionPoint = content.indexOf("\n", endOfMarkerLineIndex) + 1;
    return insertionPoint;
  }

  return (
    content
      // @ts-ignore
      .replace(/title:\s*(.*)/g, `Title: Polska (POL)`)
      .replace(/^Style: default,.*$/im, `Style: default,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: internal,.*$/im, `Style: default,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: Default,.*$/im, `Style: Default,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: q0,.*$/im, `Style: q0,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: q10,.*$/im, `Style: Default,Gabarito Medium,${fontSize / 1.5},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize / 1.5},${shadowSize / 1.5},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: q11,.*$/im, `Style: Default,Gabarito Medium,${fontSize / 1.5},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize / 1.5},${shadowSize / 1.5},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: default - top,.*$/im, `Style: default - top,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: default top,.*$/im, `Style: default - top,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: Default-Top,.*$/im, `Style: default - top,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: main,.*$/im, `Style: main,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: top,.*$/im, `Style: top,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: top internal,.*$/im, `Style: top,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: italics,.*$/im, `Style: italics,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: main_italic,.*$/im, `Style: main_italic,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: italicstop,.*$/im, `Style: italicstop,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: italics - top,.*$/im, `Style: italicstop,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: italics top,.*$/im, `Style: italicstop,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: defaultitalics,.*$/im, `Style: defaultitalics,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: defaulttop,.*$/im, `Style: defaulttop,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: defaultItalicstop,.*$/im, `Style: defaultitalicstop,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: defaultoverlap,.*$/im, `Style: defaultoverlap,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: defaultoverlaptop,.*$/im, `Style: defaultoverlaptop,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: flashbackitalics,.*$/im, `Style: flashbackitalics,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: flashback italics,.*$/im, `Style: flashbackitalics,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: flashbacktop,.*$/im, `Style: flashbacktop,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: flashbackitalicstop,.*$/im, `Style: flashbackitalicstop,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: narration,.*$/im, `Style: narration,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: flashback,.*$/im, `Style: flashback,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: flashbackoverlap,.*$/im, `Style: flashbackoverlap,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: overlap,.*$/im, `Style: overlap,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: overlaptop,.*$/im, `Style: overlaptop,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: overlap iitalics,.*$/im, `Style: overlap italics,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: subtitle,.*$/im, `Style: subtitle,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: subtitle-2,.*$/im, `Style: subtitle-2,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: subtitle-3,.*$/im, `Style: subtitle-3,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: subtitle-4,.*$/im, `Style: subtitle-4,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: main - flashback,.*$/im, `Style: main - flashback,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: main - italics,.*$/im, `Style: main - italics,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: main - top,.*$/im, `Style: main - top,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: on top,.*$/im, `Style: main - top,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: main - top \+ italics,.*$/im, `Style: main - top + italics,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: on top italics,.*$/im, `Style: main - top + italics,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: flashback - italics,.*$/im, `Style: flashback - italics,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: flashback - top,.*$/im, `Style: flashback - top,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: Flashback-Top,.*$/im, `Style: flashback - top,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: flashback - top + italics,.*$/im, `Style: flashback - top + italics,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: Flashback_Italics-Top,.*$/im, `Style: flashback - top + italics,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: main screener,.*$/im, `Style: main screener,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: top screener,.*$/im, `Style: top screener,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: italics screener,.*$/im, `Style: italics screener,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: italicstop screener,.*$/im, `Style: italicstop screener,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: flashback screener,.*$/im, `Style: flashback screener,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: flashback italics screener,.*$/im, `Style: flashback italics screener,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: flashbacktop screener,.*$/im, `Style: flashbacktop screener,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: flashbackitalicstop screener,.*$/im, `Style: flashbackitalicstop screener,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: overlap screener,.*$/im, `Style: overlap screener,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: overlaptop screener,.*$/im, `Style: overlaptop screener,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: overlap italics screener,.*$/im, `Style: overlap italics screener,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: main_italics,.*$/im, `Style: main_italics,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: flashback_italics,.*$/im, `Style: flashback_italics,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},2,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: main_top,.*$/im, `Style: main_top,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
      .replace(/^Style: main_top \+ italics,.*$/im, `Style: main_top + italics,Gabarito Medium,${fontSize},&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,-1,0,0,100,100,0,0,1,${outlineSize},${shadowSize},8,${marginL},${marginR},${marginV},1`)
  );
}

/**
 * @description Updates the dialogue lines in the .ass file content with translations.
 * @param {string} content - The content of the .ass file.
 * @param {string[]} dialogueLines - An array of dialogue lines from the .ass file.
 * @param {string[]} translationLines - An array of translation lines from the .txt file.
 * @returns {string} The updated content with dialogue lines replaced with translations.
 */
function updateDialogueLines(content, dialogueLines, translationLines) {
  function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  }

  function insertAfterLine(content, marker) {
    const markerIndex = content.indexOf(marker);
    if (markerIndex === -1) return content; // If the marker isn't found, return the original content
    const endOfMarkerLineIndex = content.indexOf("\n", markerIndex) + 1;
    const insertionPoint = content.indexOf("\n", endOfMarkerLineIndex) + 1;
    return insertionPoint;
  }

  // Split the content into an array of lines and filter for dialogue lines
  let contentLines = content.split("\n").filter((line) => line.trim().startsWith("Dialogue:"));

  // Function to find the index of the nth occurrence of a character
  const findNthOccurrence = (str, char, n) => {
    let count = 0;
    for (let i = 0; i < str.length; i++) {
      if (str[i] === char) {
        count++;
        if (count === n) return i;
      }
    }
    return -1; // If the nth occurrence is not found
  };

  // Process the filtered dialogue lines
  for (let i = 0; i < contentLines.length; i++) {
    const line = contentLines[i];
    const tenthCommaIndex = findNthOccurrence(line, ",", 9);

    if (tenthCommaIndex !== -1 && i < translationLines.length) {
      const lineWithoutTranslation = line.substring(0, tenthCommaIndex + 1);
      const newTranslation = translationLines[i].trim();
      const newLine = lineWithoutTranslation + newTranslation;

      // Replace the old line with the new line
      contentLines[i] = newLine;
    }
  }

  // Join the filtered and processed lines back into a single string
  content = content
    .split("\n")
    .map((line) => (line.trim().startsWith("Dialogue:") ? contentLines.shift() : line))
    .join("\n");

  const eventsMarker = "[Events]";
  let insertIndex = insertAfterLine(content, eventsMarker);
  let beforeInsert = content.slice(0, insertIndex);
  let afterInsert = content.slice(insertIndex);
  let newLine = `Dialogue: 0,0:00:00.00,0:00:10.00,selfplug,Lycoris,0,0,0,,{\\fad(1000,1000)}Na {\\u1\\b1}lycoris.cafe{\\u0\\b0} znajdziesz harmonogram wydań, oceny znajomych oraz odcinki najszybciej w najlepszej jakości.`;
  content = `${beforeInsert}\n${newLine}\n${afterInsert}`;

  return content;
}

function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
}

/**
 * @description Retrieves the dialogue lines from the .ass file.
 * @param {string} filePath - Path to the .ass file.
 * @returns {Promise<string[]>} An array of dialogue lines.
 */
async function getDialogueLines(filePath) {
  let lines = fs.readFileSync(filePath, "utf8").split("\n");
  lines = lines.map((line) => {
    // if (!line.includes("sign")) {
    //   return line.replaceAll("\\N", "");
    // }
    // @ts-ignore
    return line.replaceAll("  ", " ");
  });
  return lines.filter((line) => line.startsWith("Dialogue:"));
}
/**
 * @description Retrieves the translation lines from the .txt file.
 * @param {string} filePath - Path to the .txt translation file.
 * @returns {Promise<string[]>} An array of translation lines.
 */
async function getTranslationLines(filePath) {
  let lines = fs.readFileSync(filePath, "utf8").split("\n");
  lines = lines.map((line) => {
    // if (!line.includes("sign")) {
    //   return line.replaceAll("\\N", "");
    // }
    // @ts-ignore
    return line.replaceAll("  ", " ");
  });
  return lines;
}

/**
 * @description Trims trailing newlines from the file.
 * @param {string} filePath - Path to the file.
 * @returns {Promise<void>}
 */
async function trimTrailingNewlines(filePath) {
  const content = fs.readFileSync(filePath, "utf8");
  const trimmedContent = content.replace(/\s+$/, "");
  fs.writeFileSync(filePath, trimmedContent, "utf8");
}

/**
 * @description Converts style names in dialogue lines to lowercase.
 * @param {string | string[]} data - The dialogue lines as a string or an array of lines.
 * @returns {string | string[]} The updated dialogue lines with style names converted to lowercase.
 */
function stylesInDialogueToLowerCase(data) {
  const lines = Array.isArray(data) ? data : data.split("\n");
  let result = [];

  lines.forEach((line) => {
    if (line.startsWith("Dialogue:")) {
      const parts = line.split(",");
      if (parts.length > 3) {
        parts[3] = parts[3].toLowerCase();
        if (parts[3].toLowerCase() === "default") {
          parts[3] = "Default";
        }
        result.push(parts.join(","));
      } else {
        result.push(line);
      }
    } else {
      result.push(line);
    }
  });

  // @ts-ignore
  return Array.isArray(data) ? result : result.join("\n");
}

/**
 * @description Converts style names in style tags to lowercase.
 * @param {string | string[]} data - The dialogue lines as a string or an array of lines.
 * @returns {string | string[]} The updated dialogue lines with style names converted to lowercase.
 */
function stylesInStyleTagToLowerCase(data) {
  const lines = Array.isArray(data) ? data : data.split("\n");
  let result = [];
  lines.forEach((line) => {
    if (line.startsWith("Style:")) {
      const parts = line.split(",");
      parts[0] = parts[0].toLowerCase();
      if (parts[0] === "default") {
        parts[0] = "Default";
      }
      // @ts-ignore
      result.push(
        parts
          .join(",")
          // @ts-ignore
          .replaceAll("style:", "Style:")
          // @ts-ignore
          .replaceAll(/\bdefault\b/g, "Default")
      );
    } else {
      result.push(line);
    }
  });
  // @ts-ignore
  return Array.isArray(data) ? result : result.join("\n");
}

/**
 * @description Extracts the title and episode number from the filename.
 * @param {string} filename - The name of the file.
 * @returns {string[]} An array containing the title and episode number.
 * @throws {Error} If unable to extract the title or episode number from the filename.
 */
function extractTitleAndEpisode(filename) {
  // Check if this is a ToonsHub file
  const isToonsHub = filename.includes('ToonsHub');

  if (isToonsHub) {
    // Handle ToonsHub filename format
    // Example: Summer.Pockets.S01E03.A.Girl.and.a.Pirate.Ship.1080p.AMZN.WEB-DL.JPN.DDP2.0.H.264.ESub-ToonsHub_eng.ass
    const toonsHubMatch = filename.match(/(.+?)\.S\d+E(\d+)\..+?(?:-ToonsHub|ToonsHub)/);
    if (toonsHubMatch) {
      // Convert dots to spaces in the title
      const title = toonsHubMatch[1].replace(/\./g, ' ');
      const episodeNumber = toonsHubMatch[2].padStart(2, '0'); // Ensure 2 digits
      console.info(`${COLORS.CYAN}[INFO] Extracted from ToonsHub file: Title="${title}", Episode=${episodeNumber}${COLORS.RESET}`);
      return [title, episodeNumber];
    }
  }

  // Handle Erai-raws filename format (original logic)
  // First clean up the string
  const cleanString = filename.replace(/\[.*?\]|\.[^.]*$/g, '').trim();

  const matchTitle = cleanString.match(/^(.*?)\s-\s[^-]*$/);
  const matchEpisode = cleanString.match(/.*\s-\s(\d+).*/);

  if (!matchTitle || !matchEpisode) {
    throw new Error(`${COLORS.RED}[ERROR] Unable to extract title or episode number from filename: ${filename}${COLORS.RESET}`);
  }

  return [matchTitle[1].trim(), matchEpisode[1]];
}

/**
 * @description Creates a directory if it doesn't exist.
 * @param {string} directory - The path to the directory.
 * @returns {void}
 */
function createDirectoryIfNotExists(directory) {
  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory);
  }
}

/**
 * @description Creates a write stream to a file.
 * @param {string} filePath - The path to the file.
 * @returns {fs.WriteStream} The created write stream.
 */
function createWriteStream(filePath) {
  return fs.createWriteStream(filePath, { flags: "a" });
}

/**
 * @description Overrides the console.info method to log messages to a file.
 * @param {fs.WriteStream} logStream - The write stream for logging.
 * @returns {void}
 */
function overrideConsoleLog(logStream) {
  const originalLog = console.info;
  console.info = (...args) => {
    const timestamp = getTimestamp();
    const logMessage = `[${timestamp}] ${args.join(" ")}\n`;
    originalLog(...args);
    logStream.write(logMessage);
  };
}

/**
 * @description Overrides the console.error method to log error messages to a file.
 * @param {fs.WriteStream} errorStream - The write stream for error logging.
 * @returns {void}
 */
function overrideConsoleError(errorStream) {
  const originalError = console.error;
  console.error = (...args) => {
    const timestamp = getTimestamp();
    const errorMessage = cleanErrorMessage(`[${timestamp}] ${args.join(" ")}\n`);
    originalError(...args);
    errorStream.write(errorMessage);
  };
}

/**
 * @description Handles uncaught exceptions and logs them to a file.
 * @param {Error} err - The uncaught exception.
 * @returns {Promise<void>}
 */
async function handleUncaughtException(err) {
  const errorMessage = cleanErrorMessage(err.toString());
  const timestamp = getTimestamp();
  const exceptionMessage = `[${timestamp}] ${err.stack}\n`;

  console.error(exceptionMessage);
  exceptionLogStream.write(exceptionMessage);

  process.exit(1);
}

/**
 * @description Handles unhandled rejections and logs them to a file.
 * @param {Error|any} reason - The reason for the unhandled rejection.
 * @param {Promise} promise - The promise that was rejected.
 * @returns {Promise<void>}
 */
async function handleUnhandledRejection(reason, promise) {
  const reasonMessage = cleanErrorMessage(reason.toString());
  const timestamp = getTimestamp();
  const rejectionMessage = `[${timestamp}]\n${reason.stack}\n`;

  console.error(rejectionMessage);
  rejectionLogStream.write(rejectionMessage);

  process.exit(1);
}

/**
 * @description Handles graceful shutdown of the application.
 * @returns {void}
 */
function handleGracefulShutdown() {
  stopRunning();

  const timestamp = getTimestamp();
  const shutdownMessage = `[${timestamp}] Graceful shutdown\n`;
  console.info(shutdownMessage);
  infoLogStream.write(shutdownMessage);

  infoLogStream.end();
  errorLogStream.end();
  exceptionLogStream.end();

  rejectionLogStream.end(() => {
    process.exit(0);
  });
}

/**
 * @description Marks the application as not running.
 * @returns {void}
 */
function stopRunning() {
  fs.writeFileSync(IS_RUNNING_FILE, "false");
}

/**
 * @description Gets the current timestamp in ISO format.
 * @returns {string} The current timestamp.
 */
function getTimestamp() {
  const now = new Date();
  return now.toISOString();
}

/**
 * @description Cleans the error message by removing ANSI escape codes.
 * @param {string} errorMessage - The error message to clean.
 * @returns {string} The cleaned error message.
 */
function cleanErrorMessage(errorMessage) {
  // @ts-ignore
  return errorMessage.replace(/\[90m/g, "").replace(/\[36m/g, "").replace(/\[37m/g, "").replace(/\[46m/g, "").replace(/\[30m/g, "").replace(/\[0m/g, "").replace(/\[31m/g, "").replaceAll("", "");
}

/**
 * @description Sets up handling for uncaught errors and graceful shutdown.
 * @returns {void}
 */
function setupUncaughtErrorHandling() {
  process.on("uncaughtException", handleUncaughtException);
  process.on("unhandledRejection", handleUnhandledRejection);
  process.on("SIGINT", handleGracefulShutdown);
}

/**
 * @description Sets up logging by overriding console methods.
 * @returns {void}
 */
function setupLogging() {
  overrideConsoleLog(infoLogStream);
  overrideConsoleError(errorLogStream);
}

async function deleteAllFilesInDirectory(directoryPath) {
  try {
    const files = fs.readdirSync(directoryPath).filter((file) => file !== ".gitkeep");
    for (const file of files) {
      const filePath = path.join(directoryPath, file);
      try {
        const stats = fs.statSync(filePath);

        if (stats.isDirectory()) {
          // Recursively delete contents of subdirectory
          await deleteAllFilesInDirectory(filePath);
          // Remove the now-empty directory
          fs.rmdirSync(filePath);
          console.log(`${COLORS.GRAY}[INFO] Deleted directory: ${filePath}${COLORS.RESET}`);
        } else {
          if (directoryPath === "app/0rss/downloads") {
            await closeFileHandles(filePath);
          }
          fs.unlinkSync(filePath);
          console.log(`${COLORS.GRAY}[INFO] Deleted file: ${filePath}${COLORS.RESET}`);
        }
      } catch (error) {
        console.error(`${COLORS.RED}[ERROR] Error deleting ${filePath}: ${error.message}${COLORS.RESET}`);
      }
    }
    console.info(`${COLORS.GRAY}[INFO] Completed deletion process for directory: ${directoryPath}${COLORS.RESET}`);
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Error processing directory: ${directoryPath}${COLORS.RESET}`);
    console.error(error);
  }
}

/**
 * @returns {Promise} A promise that resolves when the directory cleanup is complete
 * @param {string[]} dirsToClean
 */
async function performDirectoryCleanup(dirsToClean) {
  for (const dir of dirsToClean) {
    await deleteAllFilesInDirectory(dir);
  }
}

async function closeFileHandles(filePath) {
  try {
    const command = `handle.exe -nobanner -accepteula "${filePath}"`;
    const { stdout } = await execAsync(command);
    console.log(`Processes using "${filePath}":`, stdout);

    const pidRegex = /pid: (\d+)/g;
    let match;
    const processes = [];

    while ((match = pidRegex.exec(stdout)) !== null) {
      processes.push(match[1]);
    }

    if (processes.length > 0) {
      for (const pid of processes) {
        console.log(`${COLORS.GRAY}[INFO] Closing handle for process ${pid}${COLORS.RESET}`);
        await execAsync(`taskkill /PID ${pid} /F`);
      }
    } else {
      console.log(`${COLORS.GRAY}[INFO] No processes found using the file${COLORS.RESET}`);
    }
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Error closing file handles: ${error.message}${COLORS.RESET}`);
  }
}
