{"_comment": "SubsPlease Season Mapping Configuration", "_description": "This file maps anime titles to their season information for SubsPlease releases. When an episode number is >= startEpisode, the title will be modified to include the season name and episode numbers will be adjusted to start from 01.", "_example": "If 'Sono Bisque Doll wa Koi wo Suru' has startEpisode: 13 and seasonTitle: 'Sono Bisque Doll wa Koi wo Suru Season 2', then episode 13 becomes 'Season 2 - 01', episode 14 becomes 'Season 2 - 02', etc.", "mappings": {"Sono Bisque Doll wa Koi wo Suru": {"startEpisode": 13, "seasonTitle": "Sono Bisque Doll wa Koi wo Suru Season 2"}, "Jibaku Shounen Hanako-kun S2": {"startEpisode": 13, "seasonTitle": "Jibaku Shounen Hanako-kun 2 Part 2"}, "Dainanaoji": {"startEpisode": 13, "seasonTitle": "Dainanaoji Season 2"}}}